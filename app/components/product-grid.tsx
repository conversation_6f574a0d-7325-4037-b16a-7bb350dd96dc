import { NavLink } from "react-router"
import type { Product } from "~/lib/route-utils"

interface ProductCardProps {
  product: Product
  className?: string
}

export function ProductCard({ product, className }: ProductCardProps) {
  const linkProps = product.external
    ? {
        to: product.href,
        target: "_blank",
        rel: "noreferrer",
      }
    : {
        to: product.href,
        viewTransition: true,
      }

  return (
    <NavLink
      {...linkProps}
      className={`bg-card rounded-lg p-4 hover:bg-card/80 transition-colors ${className || ""}`}
    >
      <h3 className="text-lg font-bold">{product.title}</h3>
      <p className="text-sm opacity-80">{product.description}</p>
    </NavLink>
  )
}

interface ProductGridProps {
  products: Product[]
  columns?: "1" | "2" | "3"
  className?: string
}

export function ProductGrid({
  products,
  columns = "2",
  className,
}: ProductGridProps) {
  const gridClass = {
    "1": "grid-cols-1",
    "2": "grid-cols-1 md:grid-cols-2",
    "3": "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  }

  return (
    <div className={`grid ${gridClass[columns]} gap-4 ${className || ""}`}>
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
