import type { MetaDescriptor } from "react-router"

/**
 * Common meta configuration for all routes
 */
export interface MetaConfig {
  title: string
  description: string
  keywords?: string
  author?: string
  ogImage?: string
  ogUrl?: string
  twitterHandle?: string
  locale?: string
}

/**
 * Generate standardized meta descriptors
 */
export function createMeta(config: MetaConfig): MetaDescriptor[] {
  const {
    title,
    description,
    keywords,
    author = "<PERSON>",
    ogImage = "https://assets.zhanghe.dev/og.png",
    ogUrl,
    twitterHandle = "@zhanghedev",
    locale = "en_US",
  } = config

  const meta: MetaDescriptor[] = [
    { title },
    { name: "description", content: description },
    { name: "author", content: author },
    {
      name: "robots",
      content:
        "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1",
    },
    { name: "viewport", content: "width=device-width, initial-scale=1" },
    { name: "theme-color", content: "#000000" },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:image", content: ogImage },
    { property: "og:type", content: "website" },
    { property: "og:site_name", content: "Henry Zhang - Developer & Creator" },
    { property: "og:locale", content: locale },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
    { name: "twitter:image", content: ogImage },
    { name: "twitter:site", content: twitterHandle },
    { name: "twitter:creator", content: twitterHandle },
  ]

  if (keywords) {
    meta.push({ name: "keywords", content: keywords })
  }

  if (ogUrl) {
    meta.push(
      { property: "og:url", content: ogUrl },
      { tagName: "link", rel: "canonical", href: ogUrl }
    )
  }

  return meta
}

/**
 * Safe database loader wrapper with error handling
 */
export async function createSafeLoader<T>(
  fetchFn: () => Promise<T>,
  defaultValue: T,
  errorMessage = "Failed to load data"
): Promise<T> {
  try {
    return await fetchFn()
  } catch (error) {
    console.error(`${errorMessage}:`, error)
    return defaultValue
  }
}

/**
 * Product configuration interface
 */
export interface Product {
  id: string
  title: string
  description: string
  href: string
  external?: boolean
}

/**
 * Common product definitions
 */
export const PRODUCTS: Product[] = [
  {
    id: "native-translate",
    title: "Native Translate",
    description: "Private, Built‑in AI Translation",
    href: "/products/native-translate",
  },
  {
    id: "cover-moment",
    title: "Cover Moment",
    description: "Free Online Video Cover Generator",
    href: "/products/cover-moment",
  },
  {
    id: "video-clipper",
    title: "Video Clipper",
    description: "Free Online Long to Short Video Converter",
    href: "/products/video-clipper",
  },
  {
    id: "suno-lyric-downloader",
    title: "Suno Lyric Downloader",
    description: "Free Chrome Extension for Suno Lyrics (SRT/LRC)",
    href: "/products/suno-lyric-downloader",
  },
  {
    id: "alchemy",
    title: "Alchemy",
    description: "Make creation easier and inspiration freer",
    href: "https://alchemy.host",
    external: true,
  },
  {
    id: "git-commit-analyzer",
    title: "Git Commit Analyzer",
    description: "Automatic generation of Git Flow compliant commit messages",
    href: "https://github.com/zh30/git-commit-analyzer",
    external: true,
  },
  {
    id: "mangoflow",
    title: "MangoFlow",
    description: "AI Chat Sidebar for Smarter Web Browsing",
    href: "/products/mangoflow",
  },
]

/**
 * Common game definitions
 */
export const GAMES: Product[] = [
  {
    id: "deep-hunt",
    title: "Deep Hunt",
    description:
      "Master the art of stealth and sonar to hunt down your opponents in this tactical pixel-art submarine battle.",
    href: "/games/deephunt",
  },
]

/**
 * Common page layout configuration
 */
export interface PageConfig {
  title: string
  showBackButton?: boolean
  containerClass?: string
}

/**
 * Generate common page header structure
 */
export function createPageHeader(config: PageConfig) {
  return {
    title: config.title,
    containerClass:
      config.containerClass ||
      "container max-w-screen-md mx-auto min-h-[256px] p-8 gap-6 flex flex-col justify-end",
  }
}
