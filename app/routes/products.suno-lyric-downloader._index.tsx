import { Link } from "react-router"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/products.suno-lyric-downloader._index"

export function meta() {
  return createMeta({
    title:
      "Free Suno Lyric Downloader Chrome Extension | Download SRT & LRC Lyrics",
    description:
      "Free Chrome extension to download lyrics from Suno.com in SRT and LRC formats. One-click lyric extraction with perfect accuracy. Essential tool for musicians, content creators, and Suno AI users.",
    keywords:
      "Suno lyrics downloader, Chrome extension, SRT lyrics, LRC lyrics, music tools, content creator tools, Suno AI, lyric extraction, music transcription, subtitle downloader, browser extensions, free music tools",
    ogImage: "https://assets.zhanghe.dev/suno-lyric-downloader/og.png",
    ogUrl: "https://zhanghe.dev/products/suno-lyric-downloader",
  })
}

export default function SunoLyricDownloader(_: Route.ComponentProps) {
  return (
    <div className="container max-w-screen-md mx-auto px-6 py-10 md:pt-20 text-center">
      <h1
        className="text-3xl md:text-5xl font-bold inline-block mb-8"
        style={{
          viewTransitionName: "blog-title",
        }}
      >
        Suno Lyric Downloader
      </h1>
      <p className="text-lg text-muted-foreground sm:text-xl font-light opacity-80 mb-8">
        Easily download lyrics from Suno.com in SRT and LRC formats with just
        one click using the Suno Lyric Downloader Chrome extension. Get accurate
        lyrics instantly. Free download!
      </p>
      <Link
        to="https://chromewebstore.google.com/detail/suno-lyric-downloader/hhplbhnaldbldkgfkcfjklfneggokijm?authuser=0&hl=en"
        target="_blank"
        rel="noreferrer"
        className="cursor-pointer"
      >
        <Button className="cursor-pointer" size="lg">
          Get it for Free
        </Button>
      </Link>
      <div className="mt-12 flex justify-center">
        <iframe
          className="aspect-video w-full max-w-2xl rounded-lg shadow-xl"
          src="https://www.youtube.com/embed/_7x9GdWjreY?si=Fl8wxpOzuZRwnyqK"
          title="Suno Lyric Downloader Demo"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerPolicy="strict-origin-when-cross-origin"
          allowFullScreen
        />
      </div>
    </div>
  )
}
