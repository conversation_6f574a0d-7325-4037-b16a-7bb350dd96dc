import { Outlet } from "react-router"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/posts"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "<PERSON>'s Blog | Web Development & AI Technology Insights",
    description: "Comprehensive blog covering web development, AI technology, software engineering, and digital innovation. Expert tutorials, insights, and thought leadership for developers and tech enthusiasts.",
    keywords: "technology blog, web development, AI insights, software engineering, programming tutorials, tech articles, developer resources, coding guides, tech innovation, digital transformation",
    ogUrl: "https://zhanghe.dev/posts",
  })
}

export async function loader(_: Route.LoaderArgs) {
  // Layout loader - could load common blog data here
  return { blogInfo: { title: "<PERSON>'s Blog" } }
}

export default function BlogLayout(_: Route.ComponentProps) {
  return <Outlet />
}
