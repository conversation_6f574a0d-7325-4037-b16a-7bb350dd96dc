import { createMeta } from "~/lib/route-utils"

export function meta() {
  return createMeta({
    title: "Privacy Policy - Native Translate",
    description:
      "Privacy policy for the Native Translate Chrome extension. Local-first translation and detection using Chrome's on-device AI. No analytics or external telemetry.",
    ogUrl: "https://zhanghe.dev/products/native-translate/privacy",
  })
}

export default function PrivacyPolicy() {
  return (
    <div className="container max-w-screen-md mx-auto px-6 py-10 md:pt-20">
      <h1
        className="text-3xl md:text-4xl font-bold inline-block mb-8 text-center"
        style={{
          viewTransitionName: "privacy-title",
        }}
      >
        Native Translate Privacy Policy
      </h1>
      <div className="prose lg:prose-lg dark:prose-invert max-w-none">
        <p>
          <strong>Effective Date:</strong> 2025-08-14
        </p>
        <p>
          This Privacy Policy explains how the Native Translate browser
          extension (&ldquo;Extension&rdquo;, &ldquo;we&rdquo;,
          &ldquo;us&rdquo;, or &ldquo;our&rdquo;) processes information. We
          designed Native Translate to be privacy‑first: translation and
          language detection run on your device using Chrome&rsquo;s built‑in
          on‑device AI. By using the Extension, you agree to this Policy.
        </p>

        <h3>Scope</h3>
        <p>
          This Policy applies solely to the Native Translate Chrome extension.
          It does not cover Chrome, the Chrome Web Store, your operating system,
          or any websites you visit.
        </p>

        <h3>Information We Process</h3>
        <p>
          <strong>Extension settings (stored locally):</strong> We store minimal
          preferences in Chrome storage, such as:
        </p>
        <ul>
          <li>
            <code>nativeTranslate.settings</code> (e.g., target language, hotkey
            modifier),
          </li>
          <li>
            <code>nativeTranslate:readyPairs</code> (language‑pair readiness
            timestamps),
          </li>
          <li>
            <code>nativeTranslate:detectorReady</code> (language detector
            readiness timestamp).
          </li>
        </ul>
        <p>These values stay on your device and are not transmitted to us.</p>
        <p>
          <strong>
            On‑page text for translation (processed locally, not stored):
          </strong>{" "}
          When you request translation, the Extension reads visible text nodes
          on the current page to generate translations. It avoids editing
          contexts and sensitive elements where feasible (e.g., input, textarea,
          code, pre, navigation, headers/footers). Text is processed in memory
          and not saved or transmitted.
        </p>
        <p>
          <strong>Keyboard modifier state (processed locally):</strong> To
          support hover‑to‑translate, the Extension listens for
          Alt/Control/Shift to detect activation. It does not record keystroke
          content and ignores typing contexts where feasible.
        </p>
        <p>
          <strong>Diagnostics (local only):</strong> Limited console logs may be
          written to your local browser console for reliability. No analytics,
          telemetry, or remote logging are performed.
        </p>

        <h3>What We Do Not Collect</h3>
        <ul>
          <li>No personal account data (e.g., names, emails, passwords).</li>
          <li>
            No browsing history collection beyond the active tab context needed
            to run.
          </li>
          <li>No unique advertising identifiers.</li>
          <li>No server‑side logs and no external telemetry.</li>
        </ul>

        <h3>How Translation Works (Local‑First)</h3>
        <ul>
          <li>
            <strong>On‑device translation and detection:</strong> The Extension
            uses Chrome&rsquo;s built‑in Translator and Language Detector APIs
            to process text on your device. By default, no cloud translation
            requests are made by the Extension.
          </li>
          <li>
            <strong>Model downloads are managed by Chrome:</strong> On first
            use, Chrome may download on‑device models and cache them for offline
            use. These downloads are handled by Chrome, not by us, and may be
            subject to Chrome/Google policies.
          </li>
        </ul>

        <h3>Permissions We Request</h3>
        <ul>
          <li>
            <code>storage</code>: Save local settings and readiness metadata.
          </li>
          <li>
            <code>activeTab</code> / <code>tabs</code>: Interact with the
            current tab to send translation commands.
          </li>
          <li>
            <code>scripting</code>: Inject the content script when needed to
            perform translation.
          </li>
          <li>
            <code>sidePanel</code>: Provide an optional side panel UI.
          </li>
        </ul>
        <p>We request only the permissions necessary for core functionality.</p>

        <h3>Data Sharing and Transfers</h3>
        <ul>
          <li>
            <strong>No third‑party sharing:</strong> We do not sell, rent, or
            share your information with third parties.
          </li>
          <li>
            <strong>No cross‑border transfers:</strong> The Extension does not
            transmit your data to any external servers.
          </li>
        </ul>

        <h3>Data Retention</h3>
        <ul>
          <li>
            <strong>Local preferences:</strong> Remain in Chrome&rsquo;s local
            or session storage until you remove the Extension or clear
            site/extension data.
          </li>
          <li>
            <strong>In‑memory caches:</strong> Translation results may be cached
            in memory during the session to improve performance and are
            discarded when the page or browser is closed.
          </li>
          <li>
            <strong>No long‑term storage of page content:</strong> We do not
            persist on‑page text.
          </li>
        </ul>

        <h3>Security</h3>
        <ul>
          <li>
            <strong>Local processing</strong> reduces exposure by keeping
            translation on your device.
          </li>
          <li>
            <strong>Open‑source code:</strong> The project is open source,
            allowing community review. As with any software, no method is 100%
            secure, and you use the Extension at your own risk.
          </li>
        </ul>

        <h3>Your Choices</h3>
        <ul>
          <li>
            <strong>Change or remove settings:</strong> Adjust preferences in
            the popup or clear extension data via your browser settings.
          </li>
          <li>
            <strong>Disable or uninstall:</strong> You can disable or remove the
            Extension at any time from <code>chrome://extensions</code>.
          </li>
          <li>
            <strong>Site limitations:</strong> Some pages (e.g.,{" "}
            <code>chrome://</code>) do not allow script injection; the Extension
            will not run there.
          </li>
        </ul>

        <h3>Children’s Privacy</h3>
        <p>
          The Extension is not directed to children and does not knowingly
          collect personal information from children.
        </p>

        <h3>Changes to This Policy</h3>
        <p>
          We may update this Policy from time to time. Material changes will be
          reflected by updating the Effective Date above. Your continued use
          after changes indicates acceptance.
        </p>
      </div>
    </div>
  )
}
