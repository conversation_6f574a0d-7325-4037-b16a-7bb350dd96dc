import { PageHeader } from "~/components/page-layout"
import { ProductGrid } from "~/components/product-grid"
import { createMeta, PRODUCTS } from "~/lib/route-utils"
import type { Route } from "./+types/products._index"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title:
      "Free AI Tools & Web Applications by <PERSON> | Product Collection",
    description:
      "Discover free AI-powered tools and web applications designed for content creators, developers, and digital professionals. Video editors, Chrome extensions, and productivity tools - all completely free.",
    keywords:
      "free AI tools, web applications, Chrome extensions, video editing tools, productivity apps, content creation software, developer tools, digital marketing tools, browser extensions, online utilities",
    ogUrl: "https://zhanghe.dev/products",
  })
}

export async function loader(_: Route.LoaderArgs) {
  return { products: PRODUCTS }
}

export default function ProductsIndex({ loaderData }: Route.ComponentProps) {
  const { products } = loaderData

  return (
    <>
      <PageHeader
        title="All Products"
        description="Useful tools and applications designed to enhance your productivity"
      />
      <section className="container max-w-screen-md p-8 mx-auto">
        <ProductGrid products={products} columns="1" />
      </section>
    </>
  )
}
