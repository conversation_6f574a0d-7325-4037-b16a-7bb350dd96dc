import { PageHeader } from "~/components/page-layout"
import Post<PERSON><PERSON> from "~/components/post-list"
import { DatabaseQueries } from "~/lib/database-utils"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/posts._index"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "Web Development & AI Blog | <PERSON>'s Technical Insights",
    description: "Expert insights on web development, AI technology, software engineering, and digital creativity. Learn about React, TypeScript, AI tools, and modern web development practices.",
    keywords: "web development blog, AI technology, software engineering, React tutorials, TypeScript guides, frontend development, backend development, full-stack development, programming tutorials, tech insights",
    ogUrl: "https://zhanghe.dev/posts",
  })
}

export async function loader({ context }: Route.LoaderArgs) {
  const posts = await DatabaseQueries.getAllPosts(context.db)
  return { posts }
}

export default function PostsIndex({ loaderData }: Route.ComponentProps) {
  const { posts } = loaderData

  return (
    <>
      <PageHeader
        title="All Posts"
        description="Insights, tutorials, and thoughts on web development and technology"
      />
      <section className="container max-w-screen-md p-8 mx-auto">
        <PostList posts={posts} />
      </section>
    </>
  )
}
