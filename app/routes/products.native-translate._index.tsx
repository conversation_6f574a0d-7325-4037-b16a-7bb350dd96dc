import { <PERSON> } from "react-router"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/products.native-translate._index"

export function meta() {
  return createMeta({
    title: "Native Translate — Private, Built‑in AI Translation",
    description:
      "Open‑source, privacy‑first Chrome extension using Chrome’s built‑in on‑device Translator & Language Detector. No cloud calls or telemetry. Local models with progress, caching, and offline support.",
    keywords:
      "Native Translate, Chrome extension, on-device translation, AI translation, language detection, privacy-first, offline, open-source, minimal permissions, hover-to-translate, full-page translation, RTL, LTR, caching, progress overlay",
    ogImage: "https://assets.zhanghe.dev/native-translate/og.png",
    ogUrl: "https://zhanghe.dev/products/native-translate",
  })
}

export default function NativeTranslate(_: Route.ComponentProps) {
  return (
    <div className="container max-w-screen-md mx-auto px-6 py-10 md:pt-20 text-center">
      <h1
        className="text-3xl md:text-5xl font-bold inline-block mb-6"
        style={{
          viewTransitionName: "blog-title",
        }}
      >
        Native Translate
      </h1>
      <p className="text-lg text-muted-foreground sm:text-xl font-light opacity-80 mb-8">
        Private, local‑first translation using Chrome’s built‑in AI Translator &
        Language Detector. No cloud calls, no telemetry — your content never
        leaves the browser.
      </p>

      <div className="flex items-center justify-center gap-3 mb-10">
        <Link
          to="https://github.com/zh30/native-translate"
          className="cursor-pointer"
          target="_blank"
          rel="noreferrer"
        >
          <Button className="cursor-pointer" size="lg">
            Get it for Free
          </Button>
        </Link>
      </div>

      {/* <div className="prose lg:prose-lg dark:prose-invert max-w-none mx-auto text-left">
        <ul>
          <li>Open‑source (MIT)</li>
          <li>Local‑first: translation and detection run on device</li>
          <li>
            Privacy by design: zero external translation requests by default
          </li>
          <li>Fast and robust: progress overlay, caching, RTL/LTR aware</li>
          <li>Minimal permissions, lightweight UI</li>
        </ul>

        <h2>Features</h2>
        <ul>
          <li>
            Full‑page in‑page translation: append translated text under the
            original blocks to preserve layout
          </li>
          <li>
            Hover‑to‑translate: hold a modifier (Alt/Control/Shift) and hover a
            paragraph to translate just that block
          </li>
          <li>
            Automatic source language detection (on device) with download
            progress overlay
          </li>
          <li>Model caching per language pair; auto reuse when available</li>
        </ul>
      </div> */}
    </div>
  )
}
